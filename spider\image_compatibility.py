import io
import os
from PIL import Image, UnidentifiedImageError # 导入 UnidentifiedImageError 捕获无法识别的图片格式
import traceback
import pathlib

#--- 如果需要支持 HEIC 格式，请取消下面两行的注释，并确保已安装 pillow-heif ---
try:
    from pillow_heif import register_heif_opener
    register_heif_opener() # 注册 HEIF 解码器
    print("HEIF opener registered.")
except ImportError:
    print("pillow-heif not installed. HEIC files will not be supported.")
except Exception as e:
     print(f"Error registering pillow-heif: {e}. HEIC might not work.")


def process_image_to_jpeg_stream(file_path: str):
    """
    打开图片文件，如果是 PNG/JPEG 返回原始内容，否则转换为 JPEG 二进制流返回。

    Args:
        file_path: 图片文件的完整路径。

    Returns:
        图片内容的二进制 bytes。

    Raises:
        FileNotFoundError: 如果文件不存在。
        UnidentifiedImageError: 如果 Pillow 无法识别图片格式。
        IOError: 如果读写文件时发生错误。
        Exception: 处理图片时发生其他未知错误。
    """

    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")

    try:
        with Image.open(file_path) as img:
            original_format = img.format            
            if original_format in ['PNG', 'JPEG', 'JPG']:
                with open(file_path, 'rb') as f:
                    return pathlib.Path(file_path).name, f.read()
            else:
                if img.mode in ('RGBA', 'P'):
                     img = img.convert('RGB')
                elif img.mode == 'L':
                     img = img.convert('RGB')
                byte_arr = io.BytesIO()
                img.save(byte_arr, format='JPEG', quality=85)
                jpeg_bytes = byte_arr.getvalue()
                new_file_path = pathlib.Path(file_path).name.split(".")[0] + ".jpeg"
                return new_file_path, jpeg_bytes
    except Exception as e:
        raise Exception(f"处理图片文件时发生未知错误: {e}") from e

if __name__ == "__main__":
    file_path = "/root/soft/HotDramaCMS/spider/data/2025/08/26/covers/3105915_8fa602fcada8cec8f26427a84581c318.png"
    process_image_to_jpeg_stream(file_path)
    