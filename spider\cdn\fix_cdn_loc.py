import traceback
import pymysql

from config import *
from logger import logger
import 
class BaseProcessor:
    """基础处理器类，包含通用的配置和方法"""

    def __init__(self, environment):
        """初始化处理器

        Args:
            environment: 环境名称 ('test' 或 'online')
        """
        if environment not in ENVIRONMENTS:
            raise ValueError(f"不支持的环境: {environment}")

        self.environment = environment
        self.env_config = ENVIRONMENTS[environment]
        self.db_config = self.env_config["database"]

    def get_mysql_connection(self):
        """获取MySQL连接"""
        try:
            connection = pymysql.connect(
                host=self.db_config["host"],
                port=int(self.db_config["port"]),
                user=self.db_config["username"],
                password=self.db_config["password"],
                database=self.db_config["name"],
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            logger.info(f"连接到{self.environment}环境数据库成功")
            return connection, "数据库连接成功"
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            return None, f"数据库连接失败: {str(e)}"

class DataSyncProcessor(BaseProcessor):
    """数据同步处理器"""

    def query_sql(self, conn, sql):
        """Executes a SQL query and returns the result."""
        msg = ""
        try:
            with conn.cursor() as cursor:
                cursor.execute(sql)
                result = cursor.fetchall()
                msg = f"Executed query: {sql} - Rows returned: {len(result)}"
                return result, msg
        except pymysql.MySQLError as e:
            msg = f"Error executing query: {traceback.format_exc()}"
            return None, msg

    def execute_sql(self, conn, sql):
        """Executes a SQL command and returns the number of affected rows."""
        try:
            with conn.cursor() as cursor:
                cursor.execute(sql)
                conn.commit()
                msg = f"Executed command: {sql} - Rows affected: {cursor.rowcount}"
                return cursor.rowcount, msg
        except pymysql.MySQLError as e:
            msg = f"Error executing command: {traceback.format_exc()}"
            conn.rollback()
            return None, msg

    def fix_cdn_urls(self, page_size=10, total = 10):
        """
        修正files表中的URL，添加CDN前缀

        Args:
            page_size: 每页处理的记录数，默认10条

        Returns:
            str: 处理结果消息
        """
        cdn_prefix = "http://cdn-poly.oss-us-east-1.aliyuncs.com/public"

        conn, msg = self.get_mysql_connection()
        if not conn:
            return f"数据库连接失败: {msg}"

        try:
            # 获取总记录数
            count_sql = "SELECT COUNT(*) as total FROM files"
            count_result, count_msg = self.query_sql(conn, count_sql)
            if not count_result:
                return f"获取记录总数失败: {count_msg}"

            total_records = count_result[0]['total']
            logger.info(f"总共需要处理 {total_records} 条记录")

            processed = 0
            updated = 0
            errors = []

            # 分页处理
            for offset in range(0, total, page_size):
                # 获取当前页的数据
                select_sql = f"SELECT id, url, formats FROM files where provider = 'strapi-provider-upload-oss' LIMIT {page_size} OFFSET {offset}"
                files_data, select_msg = self.query_sql(conn, select_sql)

                if not files_data:
                    logger.warning(f"第 {offset//page_size + 1} 页没有数据: {select_msg}")
                    continue

                logger.info(f"处理第 {offset//page_size + 1} 页，共 {len(files_data)} 条记录")

                for file_record in files_data:
                    try:
                        file_id = file_record['id']
                        original_url = file_record['url']
                        original_formats = file_record['formats']

                        # 修正主URL
                        new_url = self._add_cdn_prefix(original_url, cdn_prefix)

                        # 修正formats中的URL
                        new_formats = self._fix_formats_urls(original_formats, cdn_prefix)

                        # 检查是否需要更新
                        if new_url != original_url or new_formats != original_formats:
                            # 更新数据库
                            update_sql = """
                                UPDATE files
                                SET url = %s, formats = %s
                                WHERE id = %s
                            """

                            with conn.cursor() as cursor:
                                cursor.execute(update_sql, (new_url, new_formats, file_id))
                                conn.commit()

                            updated += 1
                            logger.info(f"更新文件 ID {file_id}: {original_url} -> {new_url}")

                        processed += 1

                    except Exception as e:
                        error_msg = f"处理文件 ID {file_record.get('id', 'unknown')} 时出错: {str(e)}"
                        errors.append(error_msg)
                        logger.error(error_msg)
                        continue

            # 生成结果消息
            result_msg = f"URL修正完成！\n"
            result_msg += f"总处理记录数: {processed}\n"
            result_msg += f"实际更新记录数: {updated}\n"

            if errors:
                result_msg += f"错误数量: {len(errors)}\n"
                result_msg += "错误详情:\n" + "\n".join(errors[:10])  # 只显示前10个错误
                if len(errors) > 10:
                    result_msg += f"\n... 还有 {len(errors) - 10} 个错误"

            logger.info(result_msg)
            return result_msg

        except Exception as e:
            error_msg = f"修正URL过程中发生异常: {str(e)}"
            logger.error(error_msg)
            return error_msg
        finally:
            conn.close()

    def _add_cdn_prefix(self, url, cdn_prefix):
        """
        为URL添加CDN前缀

        Args:
            url: 原始URL
            cdn_prefix: CDN前缀

        Returns:
            str: 添加前缀后的URL
        """
        if not url:
            return url

        # 如果URL已经包含CDN前缀，则不重复添加
        if url.startswith(cdn_prefix):
            if url.find("public/public") > 0:
                url = url.replace("public/public", "public")
            return url

        # 如果URL以/uploads开头，添加CDN前缀
        if url.startswith('/uploads'):
            return cdn_prefix + url

        # 如果URL已经是完整的HTTP URL，则不修改
        if url.startswith('http://') or url.startswith('https://'):
            return url

        # 其他情况，添加CDN前缀
        return cdn_prefix + ('/' + url if not url.startswith('/') else url)

    def _fix_formats_urls(self, formats_json, cdn_prefix):
        """
        修正formats JSON中的URL

        Args:
            formats_json: formats字段的JSON字符串
            cdn_prefix: CDN前缀

        Returns:
            str: 修正后的JSON字符串
        """
        if not formats_json:
            return formats_json

        try:
            import json
            formats_data = json.loads(formats_json) if isinstance(formats_json, str) else formats_json

            # 遍历formats中的每个尺寸
            for size_key, size_data in formats_data.items():
                if isinstance(size_data, dict) and 'url' in size_data:
                    original_url = size_data['url']
                    new_url = self._add_cdn_prefix(original_url, cdn_prefix)
                    size_data['url'] = new_url

            return json.dumps(formats_data, ensure_ascii=False)

        except Exception as e:
            logger.error(f"修正formats URL时出错: {str(e)}")
            return formats_json

if __name__ == "__main__":

    processor = DataSyncProcessor("test")
    result = processor.fix_cdn_urls(page_size=1)
    print(result)
               