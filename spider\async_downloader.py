import asyncio
import aiohttp
import aiofiles
import os
import ssl
from urllib.parse import urlparse
from logger import logger

class AsyncDownloader:
    def __init__(self, max_concurrent=10, timeout=30, max_retries=3):
        self.max_concurrent = max_concurrent
        self.timeout = timeout
        self.max_retries = max_retries
        self.semaphore = asyncio.Semaphore(max_concurrent)

        # 创建SSL上下文，允许不安全的连接
        self.ssl_context = ssl.create_default_context()
        self.ssl_context.check_hostname = False
        self.ssl_context.verify_mode = ssl.CERT_NONE
    
    async def download_file(self, session, url, filepath, item_id):
        """异步下载单个文件，带重试机制"""
        async with self.semaphore:
            last_error = None

            for attempt in range(self.max_retries):
                try:
                    timeout = aiohttp.ClientTimeout(total=self.timeout)
                    async with session.get(url, timeout=timeout, ssl=self.ssl_context) as response:
                        response.raise_for_status()

                        os.makedirs(os.path.dirname(filepath), exist_ok=True)

                        if not os.path.exists(filepath):
                            async with aiofiles.open(filepath, 'wb') as f:
                                async for chunk in response.content.iter_chunked(8192):
                                    await f.write(chunk)

                        logger.info(f"下载成功: {item_id} -> {filepath}")
                        return True, f"下载成功: {item_id}"

                except (aiohttp.ClientError, aiohttp.ServerTimeoutError, ssl.SSLError,
                        asyncio.TimeoutError, OSError) as e:
                    last_error = e
                    error_msg = str(e)

                    # 特殊处理443 SSL错误
                    if "443" in error_msg or "SSL" in error_msg.upper() or "CERTIFICATE" in error_msg.upper():
                        logger.warning(f"SSL连接错误 (尝试 {attempt + 1}/{self.max_retries}): {item_id} - {error_msg}")
                        if attempt < self.max_retries - 1:
                            await asyncio.sleep(2 ** attempt)  # 指数退避
                            continue
                    else:
                        logger.warning(f"下载错误 (尝试 {attempt + 1}/{self.max_retries}): {item_id} - {error_msg}")
                        if attempt < self.max_retries - 1:
                            await asyncio.sleep(1)
                            continue

                except Exception as e:
                    last_error = e
                    logger.warning(f"未知错误 (尝试 {attempt + 1}/{self.max_retries}): {item_id} - {str(e)}")
                    if attempt < self.max_retries - 1:
                        await asyncio.sleep(1)
                        continue

            # 所有重试都失败了
            error_msg = f"下载失败 (已重试{self.max_retries}次): {url} - {str(last_error)}"
            logger.error(error_msg)
            return False, error_msg
    
    async def download_batch(self, download_tasks, progress_callback=None):
        """批量异步下载，确保progress回调始终被调用"""
        results = []
        completed = 0
        total = len(download_tasks)

        if total == 0:
            logger.info("没有下载任务")
            if progress_callback:
                progress_callback(0, 0)
            return results

        # 创建连接器，配置SSL设置
        connector = aiohttp.TCPConnector(
            ssl=self.ssl_context,
            limit=self.max_concurrent,
            limit_per_host=self.max_concurrent,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )

        timeout = aiohttp.ClientTimeout(total=self.timeout)

        try:
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            ) as session:
                tasks = []
                for url, filepath, item_id in download_tasks:
                    task = self.download_file(session, url, filepath, item_id)
                    tasks.append(task)

                # 确保即使出现异常，progress回调也能正常工作
                try:
                    for coro in asyncio.as_completed(tasks):
                        try:
                            result = await coro
                            results.append(result)
                        except Exception as e:
                            # 即使单个任务失败，也要记录结果并继续
                            logger.error(f"任务执行异常: {str(e)}")
                            results.append((False, f"任务执行异常: {str(e)}"))

                        completed += 1

                        # 确保progress回调始终被调用
                        if progress_callback:
                            try:
                                progress_callback(completed, total)
                            except Exception as e:
                                logger.error(f"Progress回调异常: {str(e)}")

                except Exception as e:
                    logger.error(f"批量下载过程中出现异常: {str(e)}")
                    # 确保未完成的任务也有结果记录
                    while len(results) < total:
                        results.append((False, f"批量下载异常: {str(e)}"))
                        completed += 1
                        if progress_callback:
                            try:
                                progress_callback(completed, total)
                            except Exception as callback_e:
                                logger.error(f"Progress回调异常: {str(callback_e)}")

        except Exception as e:
            logger.error(f"创建会话失败: {str(e)}")
            # 确保所有任务都有失败结果
            results = [(False, f"会话创建失败: {str(e)}") for _ in range(total)]
            if progress_callback:
                try:
                    progress_callback(total, total)
                except Exception as callback_e:
                    logger.error(f"Progress回调异常: {str(callback_e)}")

        logger.info(f"批量下载完成: 成功 {sum(1 for success, _ in results if success)}/{total}")
        return [success for success, _ in results]

# 全局下载器实例
downloader = AsyncDownloader()