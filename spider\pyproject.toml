[project]
name = "spider"
version = "0.1.0"
description = "HotDrama Spider Project"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiofiles>=24.1.0",
    "aiohttp>=3.12.15",
    "beautifulsoup4>=4.13.3",
    "google-genai>=1.16.1",
    "gradio>=5.23.3",
    "langchain-ollama>=0.3.3",
    "lxml>=6.0.1",
    "moviepy>=2.1.2",
    "numpy>=2.2.4",
    "openai>=1.97.0",
    "opencv-python>=*********",
    "openpyxl>=3.1.5",
    "pandas>=2.2.3",
    "pillow>=10.4.0",
    "pillow-heif>=0.22.0",
    "playwright>=1.51.0",
    "pymysql>=1.1.1",
    "requests>=2.31.0",
    "streamlit>=1.45.1",
    "whisper>=1.1.10",
]

[[tool.uv.index]]
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple/"
default = true
