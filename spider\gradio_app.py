import gradio as gr
import requests
import os
import json
import asyncio
import traceback
from urllib.parse import urlparse

from config import *
from logger import logger
from async_downloader import downloader
from processor import DataSyncProcessor, VideoSummaryProcessor, DatabaseManager
from datetime import datetime, timedelta
import shutil
from moviepy import VideoFileClip

# 全局数据库管理器实例
db_manager = DatabaseManager()

def read_headers_from_text(text: str):
    """从文本读取请求头"""
    headers = {}
    for line in text.split("\n"):
        line = line.strip()
        if line and ':' in line:
            key, value = line.split(':', 1)
            headers[key.strip()] = value.strip()
    return headers

def fetch_api_data(url, headers, params=None):
    """获取API数据"""
    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        logger.info(f"API请求成功: {url}")
        return response.json()
    except Exception as e:
        logger.error(f"API请求失败: {url} - {traceback.format_exc()}")
        return None
    
def fetch_post_data(url, headers, params=None):
    """获取API数据"""
    try:
        response = requests.post(url, headers=headers, params=params)
        response.raise_for_status()
        logger.info(f"API请求成功: {url}")
        return response.json()
    except Exception as e:
        logger.error(f"API请求失败: {url} - {traceback.format_exc()}")
        return None
    
def crawl_hot(day, intersection, progress=gr.Progress()):
    """爬取热力榜数据"""
    logger.info("开始爬取热力榜数据")
    progress(0, desc="初始化...")
    
    headers = read_headers_from_text(intersection)
    if not headers:
        return "错误: 无效的请求头"
    
    params = {"pageId": "1", "pageSize": "30", "day": day}
    
    progress(0.2, desc="获取数据...")
    page_data = {}
    if type(API_URLS["hot_ranking"]) is list:
        for url in API_URLS["hot_ranking"]:
            progress(0.3, desc=f"获取 {url} 榜单")
            temp_data = fetch_api_data(url, headers, params)
            if temp_data:
                for key, value in temp_data.items():
                    if key in page_data and type(page_data[key]) is list:
                        page_data[key].extend(value)
                    else:
                        page_data[key] = value
    else:
        page_data = fetch_api_data(API_URLS["hot_ranking"], headers, params)

    if not page_data:
        return "API请求失败，请检查网络连接和请求头"

    params["playletNameLangList"]="en"
    oversea_data = fetch_post_data(OVERSEA_API_URLS["hot_ranking"], headers, params)

    if oversea_data:
        for key, value in oversea_data.items():
            if key in page_data and type(page_data[key]) is list:
                page_data[key].extend(value)
            else:
                page_data[key] = value

    # 准备下载任务
    download_tasks = []
    image_save_dir = f"./data/{day.replace('-', '/')}/covers"
    os.makedirs(image_save_dir, exist_ok=True)

    # 清理7天前的旧数据
    before_day = datetime.strptime(day, "%Y-%m-%d") - timedelta(days=14)
    before_day_str = before_day.strftime("%Y%m%d")
    shutil.rmtree(f"./data/{before_day_str}", ignore_errors=True)
    progress(0.4, desc="准备下载任务...")

    for item in page_data.get("content", []):
        if isinstance(item, dict):
            playlet_id = item.get("playletId", "")
            image_url = item.get("coverOss", "") or item.get("cover", "")
            parsed_url = urlparse(image_url)
            filename = os.path.basename(parsed_url.path)
            if image_url and playlet_id:
                filename = f"{playlet_id}_{filename}"
                filepath = os.path.join(image_save_dir, filename)
                download_tasks.append((image_url, filepath, playlet_id))
    
    # 异步下载
    if download_tasks:
        progress(0.6, desc="下载图片...")
        
        def download_progress(completed, total):
            progress(0.6 + 0.3 * (completed / total), 
                    desc=f"下载进度: {completed}/{total}")
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        results = loop.run_until_complete(
            downloader.download_batch(download_tasks, download_progress)
        )
        loop.close()
    
    # 保存数据
    progress(0.9, desc="保存数据...")
    data_save_dir = f"./data/{day.replace('-', '/')}"
    os.makedirs(data_save_dir, exist_ok=True)
    
    with open(f"{data_save_dir}/data.json", "w", encoding="utf-8") as f:
        json.dump(page_data, f, ensure_ascii=False, indent=4)
    
    progress(1.0, desc="完成")
    logger.info(f"热力榜数据爬取完成, 下载封面 {len(results)}/{len(download_tasks)}")
    return f"热力榜数据爬取完成，{len(results)}/{len(download_tasks)}, 保存到: {data_save_dir}/data.json"

def download_media_files(idx, total_media, playlet_id, day, detail_data, progress_callback=None):
    """下载媒体文件（图片和视频）"""
    base_dir = f"./data/{day.replace('-', '/')}/{playlet_id}"
    images_dir = f"{base_dir}/images"
    videos_dir = f"{base_dir}/videos"

    os.makedirs(images_dir, exist_ok=True)
    os.makedirs(videos_dir, exist_ok=True)

    download_tasks = []
    downloaded_video = True
    # 处理物料列表中的视频和图片
        
    material_list = detail_data.get("content", {}).get("materialList", [])
    for _, material in enumerate(material_list):

        # 处理视频 只下载第一个存在的视频
        video_url = material.get("videoUrl", "")
        if video_url and video_url.startswith("http") and downloaded_video:

            parsed_url = urlparse(video_url)        
            filename = os.path.basename(parsed_url.path)
            if not filename:
                filename = f"{playlet_id}_video.mp4"
            else:
                filename = f"{playlet_id}_{filename}"
            existing_path = db_manager.check_entity_exists(filename)
            if not existing_path or not os.path.exists(existing_path):
                db_manager.delete_entity_data(filename)
                video_path = os.path.join(videos_dir, filename)
                download_tasks.append((video_url, video_path, playlet_id, filename))
            downloaded_video = False

        # 处理图片
        image_url = material.get("cover", "")
        if image_url:
            parsed_url = urlparse(image_url)        
            filename = os.path.basename(parsed_url.path)
            if not filename:
                filename = f"{playlet_id}_image.jpg"
            else:
                filename = f"{playlet_id}_{filename}"

            existing_path = db_manager.check_entity_exists(filename)
            if not existing_path or not os.path.exists(existing_path):
                db_manager.delete_entity_data(filename)
                image_path = os.path.join(images_dir, filename)
                download_tasks.append((image_url, image_path, playlet_id, filename))

    # 执行下载
    if download_tasks:
        logger.info(f"开始下载 {len(download_tasks)} 个媒体文件")

        def download_progress(completed, total):
            if progress_callback:
                progress_callback(idx, total_media, completed, total, f"下载媒体文件: {completed}/{total}")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # 准备异步下载任务
        async_tasks = [(url, path, playlet_id) for url, path, playlet_id, _ in download_tasks]
        results = loop.run_until_complete(
            downloader.download_batch(async_tasks, download_progress)
        )
        loop.close()

        # 保存下载记录到数据库
        for (_, entity_path, playlet_id, entity_name), success in zip(download_tasks, results):
            if success:
                db_manager.save_entity_data(entity_name, entity_path)

        logger.info(f"媒体文件下载完成，成功: {len(results)}/{len(download_tasks)}")
        return len(results)
    return 0

def download_oversea_files(idx, total_media, playlet_id, day, detail_data, progress_callback=None):
    """下载媒体文件（图片和视频）"""
    base_dir = f"./data/{day.replace('-', '/')}/{playlet_id}"
    images_dir = f"{base_dir}/images"
    videos_dir = f"{base_dir}/videos"

    os.makedirs(images_dir, exist_ok=True)
    os.makedirs(videos_dir, exist_ok=True)

    download_tasks = []
    # 处理物料列表中的视频和图片

    if "statusCode" in detail_data and detail_data["statusCode"] != 200:
        logger.info(f"登录信息失效，请重新登录。")
        return 0
        
    material_list = detail_data.get("content", {}).get("materialList", [])

    for _, material in enumerate(material_list):
    
        # 处理视频 只下载第一个存在的视频
        video_urls = material.get("videoList", [])
        if len(video_urls) > 0:
            video_url = video_urls[0]
            if video_url and video_url.startswith("http"):
                parsed_url = urlparse(video_url)        
                filename = os.path.basename(parsed_url.path)
                if not filename:
                    filename = f"{playlet_id}_video.mp4"
                else:
                    filename = f"{playlet_id}_{filename}"
                existing_path = db_manager.check_entity_exists(filename)
                if not existing_path or not os.path.exists(existing_path):
                    db_manager.delete_entity_data(filename)
                    video_path = os.path.join(videos_dir, filename)
                    download_tasks.append((video_url, video_path, playlet_id, filename))

    # 执行下载
    if download_tasks:
        logger.info(f"开始下载 {len(download_tasks)} 个媒体文件")

        def download_progress(completed, total):
            if progress_callback:
                progress_callback(idx, total_media, completed, total, f"下载媒体文件: {completed}/{total}")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # 准备异步下载任务
        async_tasks = [(url, path, playlet_id) for url, path, playlet_id, _ in download_tasks]
        results = loop.run_until_complete(
            downloader.download_batch(async_tasks, download_progress)
        )
        loop.close()

        # 保存下载记录到数据库
        for (_, entity_path, playlet_id, entity_name), success in zip(download_tasks, results):
            if success:
                db_manager.save_entity_data(entity_name, entity_path)
                #每个视频抽一阵图片
                with VideoFileClip(str(video_path)) as video:
                    image_name = entity_name.replace(".mp4", ".jpg")
                    image_path = f"{images_dir}/{image_name}"
                    frames = video.duration - 10 if video.duration - 10 > 0 else 0
                    video.save_frame(str(image_path), t=frames)
        
        logger.info(f"媒体文件下载完成，成功: {len(results)}/{len(download_tasks)}")
        return len(results)
    
    return 0

def download_maker_team_files(idx, total_media, playlet_id, day, maker_team_data, progress_callback=None):
    """下载幕后团队文件"""
    base_dir = f"./data/{day.replace('-', '/')}/{playlet_id}"
    images_dir = f"{base_dir}/images"

    os.makedirs(images_dir, exist_ok=True)
    download_tasks = []
    
    # 处理幕后团队中的头像图片
    if "statusCode" in maker_team_data and maker_team_data["statusCode"] != 200:
        logger.error(f"幕后团队数据获取失败，请检查登录状态。")
        return 0
        
    team_list = maker_team_data.get("content", [])
    for _, member in enumerate(team_list):
        # 处理头像图片
        head_pic_url = member.get("headPic", "")
        if head_pic_url:
            parsed_url = urlparse(head_pic_url)        
            filename = os.path.basename(parsed_url.path)
            if not filename:
                continue
            else:
                filename = f"{playlet_id}_{filename}"

            existing_path = db_manager.check_entity_exists(filename)
            if not existing_path or not os.path.exists(existing_path):
                db_manager.delete_entity_data(filename)
                image_path = os.path.join(images_dir, filename)
                download_tasks.append((head_pic_url, image_path, playlet_id, filename))

    # 执行下载
    if download_tasks:
        logger.info(f"开始下载 {len(download_tasks)} 个幕后团队头像文件")

        def download_progress(completed, total):
            if progress_callback:
                progress_callback(idx, total_media, completed, total, f"下载幕后团队文件: {completed}/{total}")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # 准备异步下载任务
        async_tasks = [(url, path, playlet_id) for url, path, playlet_id, _ in download_tasks]
        results = loop.run_until_complete(
            downloader.download_batch(async_tasks, download_progress)
        )
        loop.close()

        # 保存下载记录到数据库
        for (_, entity_path, playlet_id, entity_name), success in zip(download_tasks, results):
            if success:
                db_manager.save_entity_data(entity_name, entity_path)

        logger.info(f"幕后团队文件下载完成，成功: {len(results)}/{len(download_tasks)}")
        return len(results)
    return 0

def crawl_detail_pages(day, intersection, progress=gr.Progress()):
    """爬取详情页数据"""
    logger.info("开始爬取详情页数据")
    progress(0, desc="初始化...")

    headers = read_headers_from_text(intersection)
    if not headers:
        return "错误: 无效的请求头"

    # 读取热力榜数据
    data_path = f"./data/{day.replace('-', '/')}/data.json"
    try:
        with open(data_path, encoding="utf-8") as f:
            day_data = json.load(f)
    except FileNotFoundError:
        return f"错误: 请先爬取热力榜数据 - {data_path}"

    total = len(day_data.get("content", []))
    if total == 0:
        return "错误: 没有找到需要爬取的剧集数据"

    processed = 0
    downloaded_files = 0
    data_save_dir = f"./data/{day.replace('-', '/')}"

    for i, item in enumerate(day_data.get("content", [])):
        playlet_id = item.get("playletId", "")
        adx_playlet_id = item.get("adxPlayletId", "")

        if not playlet_id:
            continue

        progress(i / total * 0.5, desc=f"爬取详情页: {playlet_id}")

        # 海外榜 
        if "adxPlayletId" in item:
            try:
                # 爬取剧集详情
                detail_url = OVERSEA_API_URLS["playlet_info"].format(playletId=playlet_id)
                detail_data = fetch_api_data(detail_url, headers)
                if detail_data and type(detail_data["content"]) != dict:
                    logger.info(f"获取剧集 {playlet_id} 信息出错, 返回内容:{detail_data}")
                    continue
                if adx_playlet_id != "":
                    maker_team_url = OVERSEA_API_URLS["making_team"].format(playletId=adx_playlet_id)
                    maker_team_data = fetch_api_data(maker_team_url, headers)
                else:
                    maker_team_data = {}
                material_url = OVERSEA_API_URLS["material_info"]
                material_list = fetch_post_data(material_url, headers, {"playletId" : playlet_id})
                content_data = {}

                if "content" in material_list and type(material_list["content"]) == list:
                    detail_data["content"]["materialList"] = material_list["content"]
                else:
                    detail_data["content"]["materialList"] = []

                detail_data["content"]["locale"] = "zh"

                content_data["getPlayletInfo"] = detail_data
                content_data["makingTeam"] = maker_team_data

                # 保存详情数据
                content_file = f"{data_save_dir}/{playlet_id}.json"
                with open(content_file, "w", encoding="utf-8") as f:
                    json.dump(content_data, f, ensure_ascii=False, indent=4)
                logger.info(f"详情页数据保存成功: {content_file}")

                # 下载媒体文件
                def media_progress(idx, total, prog, total_task, desc):
                    progress(idx / total * 0.5 + prog * 0.5 / total_task, desc=desc)

                files_downloaded = download_oversea_files(i, total, playlet_id, day, detail_data, media_progress)
                logger.info(f"媒体文件下载完成: {files_downloaded}")
                downloaded_files += files_downloaded
                processed += 1
                if maker_team_data:
                    files_downloaded = download_maker_team_files(i, total, playlet_id, day, maker_team_data, media_progress)
                    logger.info(f"幕后团队文件下载完成: {files_downloaded}")
                    downloaded_files += files_downloaded
                    processed += 1

            except Exception as e:
                logger.error(f"处理详情页失败: {playlet_id} - {traceback.format_exc()}")        
        # 热力榜和红果榜
        else:
            try:
                # 爬取剧集详情
                detail_url = API_URLS["playlet_info"].format(playletId=playlet_id)
                detail_data = fetch_api_data(detail_url, headers)
                if detail_data and type(detail_data["content"]) != dict:
                    logger.info(f"获取剧集 {playlet_id} 信息出错, 返回内容:{detail_data}")
                    continue
                maker_team_url = API_URLS["making_team"].format(playletId=playlet_id)
                maker_team_data = fetch_api_data(maker_team_url, headers)

                content_data = {}
                content_data["getPlayletInfo"] = detail_data
                content_data["makingTeam"] = maker_team_data

                # 保存详情数据
                content_file = f"{data_save_dir}/{playlet_id}.json"
                with open(content_file, "w", encoding="utf-8") as f:
                    json.dump(content_data, f, ensure_ascii=False, indent=4)
                logger.info(f"详情页数据保存成功: {content_file}")

                # 下载媒体文件
                def media_progress(idx, total, prog, total_task, desc):
                    progress(idx / total * 0.5 + prog * 0.5 / total_task, desc=desc)

                files_downloaded = download_media_files(i, total, playlet_id, day, detail_data, media_progress)
                logger.info(f"媒体文件下载完成: {files_downloaded}")
                downloaded_files += files_downloaded
                processed += 1
                if maker_team_data:
                    files_downloaded = download_maker_team_files(i, total, playlet_id, day, maker_team_data, media_progress)
                    logger.info(f"幕后团队文件下载完成: {files_downloaded}")
                    downloaded_files += files_downloaded
                    processed += 1

            except Exception as e:
                logger.error(f"处理详情页失败: {playlet_id} - {traceback.format_exc()}")

    progress(1.0, desc="完成")
    logger.info(f"详情页数据爬取完成！总计: {total}, 成功处理: {processed}, 下载文件: {downloaded_files}")
    return f"详情页数据爬取完成！总计: {total}, 成功处理: {processed}, 下载文件: {downloaded_files}"

def sync_to_environment_quickju(data_path, environment, progress=gr.Progress()):
    """同步数据到指定环境"""
    logger.info(f"开始同步数据到{environment}环境")
    progress(0, desc="初始化同步器...")

    try:
        processor = DataSyncProcessor(environment)
        filename = f"{data_path}/data.json"
        # 加载数据文件
        try:
            with open(filename, encoding="utf-8") as f:
                day_data = json.load(f)
        except FileNotFoundError:
            error_msg = f"数据文件不存在: {filename}"
            logger.error(error_msg)
            return error_msg

        # 读取AI摘要文件
        ai_summary_path = f"data/quickju/videos/ai_summary.jsonl"
        ai_summaries = {}
        if os.path.exists(ai_summary_path):
            with open(ai_summary_path, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        summary_data = json.loads(line.strip())
                        playlet_id = summary_data.get("playlet_id")
                        if playlet_id:
                            # 处理playlet_id，如果已经是负数则保持不变，否则添加负号
                            if not playlet_id.startswith('-'):
                                playlet_id = f"-{playlet_id}"
                            # 只保存第一个视频的描述
                            if playlet_id not in ai_summaries:
                                ai_summaries[playlet_id] = summary_data.get("summary", "")
                    except json.JSONDecodeError:
                        continue

        total = len(day_data["content"])
        processed = 0

        for i, item in enumerate(day_data["content"]):
            playlet_id = item["playletId"]

            processor.append_to_output(f"Start Processing playlet_id: {playlet_id}")

            # 处理剧集数据
            try:
                playlet_name = item.get("name", "")
                description = item.get("description", "")
                publish_time = item.get("publishTime", "").split(" ")[0]
                category = item.get("category", [])

                data = {
                    "playlet_id": str(playlet_id),
                    "publish_time": publish_time,
                    "name": playlet_name,
                    "description": description,
                    "keywords": category,
                    "category": category[0] if category else "未知",
                    "tag": ""
                }

                # 添加AI摘要和标签信息
                data["ai_desc"] = [ai_summaries[str(playlet_id)],] if str(playlet_id) in ai_summaries else []

                progress(i / total * 0.9, desc=f"更新剧集: {playlet_id}...")
                # 查找或创建剧集
                episodes, msg = processor.find_episodes_by_name(data)
                if episodes and len(episodes["data"]) > 0:
                    processor.append_to_output(f"找到剧集: {episodes['data'][0]['name']} (ID: {episodes['data'][0]['id']})")
                    if int(episodes["data"][0]["playlet_id"]) > 0:
                        processor.append_to_output(f"剧集 {playlet_id} 已在DataEye存在，跳过更新...")
                        continue 
                else:
                    processor.append_to_output("未找到剧集，准备创建新剧集...")

                if episodes and len(episodes["data"]) == 0:

                    episodes, msg = processor.create_episodes(data)
                    # processor.append_to_output(episodes)
                    processor.append_to_output(msg)
                    episodes, msg = processor.find_episodes(data)
                    # processor.append_to_output(episodes)
                    processor.append_to_output(msg)

                document_id = episodes["data"][0]["documentId"]
                episodes, msg = processor.update_episodes(document_id, data)
                processor.append_to_output(msg)

                # 查找剧集
                episodes, msg = processor.find_episodes(data)
                processor.append_to_output(msg)

                # 处理封面上传                
                progress(i / total * 0.9, desc=f"更新剧集封面: {playlet_id}...")
                cover_oss_local_path = f"./data/quickju/covers/{playlet_id}_cover.jpg"
                if os.path.exists(cover_oss_local_path):
                    eid = episodes["data"][0]["id"]
                    processor.append_to_output(f"Attempting to upload cover: {cover_oss_local_path}")
                    upload_response, msg = processor.upload_image(cover_oss_local_path, eid, "cover")
                    processor.append_to_output(msg)
                    msg = processor.correct_relationship(playlet_id, upload_response[0]["id"])
                    processor.append_to_output(msg)


                # 处理物料列表封面
                progress(i / total * 0.9, desc=f"更新剧集插图: {playlet_id}...")
                coverlist_dir = f"./data/quickju/coverlists/{abs(int(playlet_id))}"
                if os.path.exists(coverlist_dir):
                    for cover_file in os.listdir(coverlist_dir):
                        if cover_file.endswith('.jpg'):
                            eid = episodes["data"][0]["id"]
                            cover_path = os.path.join(coverlist_dir, cover_file)
                            processor.append_to_output(f"Attempting to upload material cover: {cover_path}")
                            upload_response, msg = processor.upload_image(cover_path, eid, "coverlist")
                            processor.append_to_output(msg)
                            msg = processor.correct_relationship(playlet_id, upload_response[0]["id"])
                            processor.append_to_output(msg)

                # 处理演员信息
                progress(i / total * 0.9, desc=f"更新剧集演员信息: {playlet_id}...")
                actors = []
                actor_list = item.get("actor", [])
                for actor_name in actor_list:
                    act = {
                        "nickname": actor_name,
                        "actType": "",
                        "headPic": ""
                    }
                    actors.append(act)
                
                # 更新演员信息
                if len(actors) > 0:
                    data["actor"] = json.dumps(actors, ensure_ascii=False)
                    _, msg = processor.update_episodes(document_id, data)
                    processor.append_to_output(msg)

                processor.append_to_output(f"Processing data for playletId {playlet_id} has finished.")
                processed += 1

            except Exception as e:
                processor.append_to_output(f"Error processing data for playletId {playlet_id}: {traceback.format_exc()}")
            
        progress(1.0, desc="同步完成")
        processor.append_to_output(f"Sync finished. Total: {total}, Processed: {processed}")
        logger.info(f"{environment}环境同步完成")
        return f"同步完成！总计: {total}, 成功处理: {processed}"

    except Exception as e:
        error_msg = f"同步失败: {str(e)}"
        logger.error(error_msg)
        return error_msg
    
def sync_to_environment(day, environment, progress=gr.Progress()):
    """同步数据到指定环境"""
    logger.info(f"开始同步数据到{environment}环境")
    progress(0, desc="初始化同步器...")

    try:
        processor = DataSyncProcessor(environment)

        # 加载数据文件
        data_path = f"./data/{day.replace('-', '/')}/data.json"
        try:
            with open(data_path, encoding="utf-8") as f:
                day_data = json.load(f)
        except FileNotFoundError:
            error_msg = f"数据文件不存在: {data_path}"
            logger.error(error_msg)
            return error_msg

        total = len(day_data["content"])
        processed = 0

        for i, item in enumerate(day_data["content"]):
            playlet_id = item["playletId"]
            progress(i / total * 0.9, desc=f"处理 {playlet_id}...")

            processor.append_to_output(f"Start Processing playlet_id: {playlet_id}")

            # 加载详细信息
            playlet_info_path = f"./data/{day.replace('-', '/')}/{playlet_id}.json"
            try:
                with open(playlet_info_path, encoding="utf-8") as f:
                    playlet_info = json.load(f)
            except FileNotFoundError:
                processor.append_to_output(f"Error: {playlet_id}.json not found for playletId {playlet_id}")
                continue

            # 处理剧集数据
            try:
                locale = playlet_info["getPlayletInfo"]["content"].get("locale", "")

                if locale == "":
                    playlet_name = playlet_info["getPlayletInfo"]["content"]["playletName"]
                    description = playlet_info["getPlayletInfo"]["content"].get("description", "")
                    publish_time = playlet_info["getPlayletInfo"]["content"].get("releaseStartDate", "")
                    playlet_tags = item.get("contentTypes", []) + item.get("playletTags", [])
                    cover_oss_path = playlet_info["getPlayletInfo"]["content"].get("coverOss", "")
                    
                    data = {
                        "playlet_id": str(playlet_id),
                        "publish_time": publish_time,
                        "name": playlet_name,
                        "description": description,
                        "keywords": playlet_tags,
                        "category": playlet_tags[0] if playlet_tags else "未知"
                    }
                    locale = "en"

                else:
                    playlet_name = playlet_info["getPlayletInfo"]["content"]["playletName"]
                    description = playlet_info["getPlayletInfo"]["content"].get("playletbrief", "")
                    publish_time = playlet_info["getPlayletInfo"]["content"].get("firstSeen", "")
                    playlet_tags = item.get("playletTags", [])
                    cover_oss_path = playlet_info["getPlayletInfo"]["content"].get("coverOss", "")
                    
                    data = {
                        "playlet_id": str(playlet_id),
                        "publish_time": publish_time,
                        "name": playlet_name,
                        "description": description,
                        "keywords": playlet_tags,
                        "category": playlet_tags[0] if playlet_tags else "未知",
                        "locale": locale
                    }

                progress(i / total * 0.9, desc=f"更新剧集: {playlet_id}...")
                # 查找或创建剧集
                episodes, msg = processor.find_episodes(data, locale)
                processor.append_to_output(msg)

                if episodes and len(episodes["data"]) == 0:
                    episodes, msg = processor.create_episodes(data, locale)
                    processor.append_to_output(msg)
                    episodes, msg = processor.find_episodes(data, locale)
                    processor.append_to_output(msg)

                document_id = episodes["data"][0]["documentId"]
                episodes, msg = processor.update_episodes(document_id, data, locale)
                processor.append_to_output(msg)

                # 查找剧集
                episodes, msg = processor.find_episodes(data, locale)
                processor.append_to_output(msg)

                # 处理封面上传                
                progress(i / total * 0.9, desc=f"更新剧集封面: {playlet_id}...")

                parsed_url = urlparse(cover_oss_path)        
                filename = os.path.basename(parsed_url.path)
                if filename:
                    filename = f"{playlet_id}_{filename}"
                    cover_oss_local_path = f"./data/{day.replace('-', '/')}/covers/{filename}"
                    if os.path.exists(cover_oss_local_path):
                        eid = episodes["data"][0]["id"]
                        processor.append_to_output(f"Attempting to upload cover: {cover_oss_local_path}")
                        upload_response, msg = processor.upload_image(cover_oss_local_path, eid, "cover")
                        processor.append_to_output(msg)
                        msg = processor.correct_relationship(playlet_id, upload_response[0]["id"])
                        processor.append_to_output(msg)

                # 处理演员信息
                progress(i / total * 0.9, desc=f"更新剧集演员信息: {playlet_id}...")
                actors = []
                if "makingTeam" in playlet_info and playlet_info["makingTeam"]:
                    making_team_data = playlet_info["makingTeam"]
                    if "content" in making_team_data and type(making_team_data["content"]) == list:
                        for actor_info in making_team_data["content"]:
                            head_pic = actor_info.get("headPic", "")
                            head_pic_path = ""
                            if head_pic:
                                parsed_url = urlparse(head_pic)        
                                filename = os.path.basename(parsed_url.path)
                                if filename:
                                    filename = f"{playlet_id}_{filename}"
                                    head_pic_path = f"./data/{day.replace('-', '/')}/{playlet_id}/images/{filename}"
                                    # 上传头像图片
                                    if os.path.exists(head_pic_path):
                                        upload_response, msg = processor.upload_image(head_pic_path)
                                        processor.append_to_output(msg)
                                        head_pic_path = upload_response[0]["url"]
                                    else:
                                        logger.info(f"演员头像不存在 {head_pic_path}")
                                # 构建演员信息
                                act_type = ",".join([identity["identity"] for identity in actor_info.get("identityList", [])])
                                act = {
                                    "nickname": actor_info.get("nickname", ""),
                                    "actType": act_type,
                                    "headPic": head_pic_path
                                }
                            actors.append(act)
                
                # 更新演员信息
                if len(actors) > 0:
                    data["actor"] = json.dumps(actors, ensure_ascii=False)
                    _, msg = processor.update_episodes(document_id, data, locale)
                    processor.append_to_output(msg)

                # 查找剧集
                episodes, msg = processor.find_episodes(data, locale)
                processor.append_to_output(msg)

                # 处理物料列表封面
                progress(i / total * 0.9, desc=f"更新剧集插图: {playlet_id}...")
                if "getPlayletInfo" in playlet_info:
                    for material in playlet_info["getPlayletInfo"]["content"]["materialList"]:
                        if isinstance(material, dict):
                            if locale == "zh":
                                video_list = material.get("videoList", [])
                                if len(video_list) > 0:
                                    video_url = video_list[0]
                                    eid = episodes["data"][0]["id"]
                                    processor.append_to_output(f"Attempting to upload material cover: {video_url}")
                                    parsed_url = urlparse(video_url)        
                                    filename = os.path.basename(parsed_url.path)
                                    if filename:
                                        filename = f"{playlet_id}_{filename.replace('.mp4', '.jpg')}"
                                        cover_path = f"./data/{day.replace('-', '/')}/{playlet_id}/images/{filename}"
                                        if os.path.exists(cover_path):
                                            upload_response, msg = processor.upload_image(cover_path, eid, "coverlist")
                                            processor.append_to_output(msg)
                                            msg = processor.correct_relationship(playlet_id, upload_response[0]["id"])
                                            processor.append_to_output(msg)                                    
                            else:
                                cover = material.get("cover", "")
                                if cover:
                                    eid = episodes["data"][0]["id"]
                                    processor.append_to_output(f"Attempting to upload material cover: {cover}")
                                    parsed_url = urlparse(cover)        
                                    filename = os.path.basename(parsed_url.path)
                                    if filename:
                                        filename = f"{playlet_id}_{filename}"
                                        cover_path = f"./data/{day.replace('-', '/')}/{playlet_id}/images/{filename}"
                                        if os.path.exists(cover_path):
                                            upload_response, msg = processor.upload_image(cover_path, eid, "coverlist")
                                            processor.append_to_output(msg)
                                            msg = processor.correct_relationship(playlet_id, upload_response[0]["id"])
                                            processor.append_to_output(msg)

                processor.append_to_output(f"Processing data for playletId {playlet_id} has finished.")
                processed += 1

            except Exception as e:
                processor.append_to_output(f"Error processing data for playletId {playlet_id}: {traceback.format_exc()}")

        progress(1.0, desc="同步完成")
        processor.append_to_output(f"Sync finished. Total: {total}, Processed: {processed}")
        logger.info(f"{environment}环境同步完成")
        return f"同步完成！总计: {total}, 成功处理: {processed}"

    except Exception as e:
        error_msg = f"同步失败: {str(e)}"
        logger.error(error_msg)
        return error_msg

def summary_videos(day, environment, progress=gr.Progress()):
    """AI解析视频摘要"""
    logger.info(f"开始{environment}环境视频AI解析")
    progress(0, desc="初始化AI处理器...")

    try:
        processor = VideoSummaryProcessor(environment, db_manager)
        sync_processor = DataSyncProcessor(environment)

        # 加载数据文件
        data_path = f"./data/{day.replace('-', '/')}/data.json"
        try:
            with open(data_path, encoding="utf-8") as f:
                day_data = json.load(f)
        except FileNotFoundError:
            error_msg = f"数据文件不存在: {data_path}"
            logger.error(error_msg)
            return error_msg

        total = len(day_data["content"])
        processed = 0

        for i, item in enumerate(day_data["content"]):
            playlet_id = item["playletId"]
            progress(i / total * 0.9, desc=f"处理 {playlet_id}...")

            processor.append_to_output(f"Processing playletId: {playlet_id}")

            # 加载详细信息
            playlet_info_path = f"./data/{day.replace('-', '/')}/{playlet_id}.json"
            try:
                with open(playlet_info_path, encoding="utf-8") as f:
                    playlet_info = json.load(f)
            except FileNotFoundError:
                processor.append_to_output(f"Error: {playlet_id}.json not found for playletId {playlet_id}")
                continue

            # 处理视频摘要
            try:
                locale =  playlet_info.get("getPlayletInfo", {}).get("content", {}).get("locale", "")
                if locale == "zh":
                    video_list = playlet_info.get("getPlayletInfo", {}).get("content", {}).get("materialList", [])
                    material_list = []
                    for material in video_list:
                        material["videoUrl"] = material["videoList"][0]
                        material_list.append(material)
                        break
                else:
                    material_list = playlet_info.get("getPlayletInfo", {}).get("content", {}).get("materialList", [])
                summaries = []
                
                for anchor_video in material_list:
                    video_url = anchor_video.get("videoUrl", "")
                    if video_url:
                        material_id = anchor_video.get("materialId", "")
                        ad_title = anchor_video.get("title", "")
                        logger.info(f"处理视频: {video_url}")
                        parsed_url = urlparse(video_url)
                        filename = os.path.basename(parsed_url.path)
                        video_path = f"./data/{day.replace('-', '/')}/{playlet_id}/videos/{playlet_id}_{filename}"
                        if not os.path.exists(video_path):
                            logger.info(f"跳过AI解析,视频文件不存在: {video_path}")
                            progress(i / total, desc=f"跳过AI解析,视频文件不存在: {video_path}")
                            continue

                        try:
                            # 检查缓存中是否已有摘要
                            cached_summary = processor.get_cached_summary_desc(material_id)
                            if cached_summary:
                                logger.info(f"使用缓存摘要: {material_id}")
                                summary = cached_summary
                            else:
                                # 生成新摘要
                                logger.info(f"开始AI分析视频: {video_path}")
                                progress(i / total * 0.9, desc=f"分析视频 {video_path}...")
                                # 分析视频文件
                                def video_progress(idx, total, desc):
                                    progress(idx / total * 0.9, desc=desc)

                                summary, msg = processor.realize_video_english(video_path, video_progress)
                                if summary:
                                    # 保存到缓存
                                    processor.save_summary(playlet_id, material_id, ad_title, video_url, summary)
                                    logger.info(f"摘要已保存到缓存: {material_id}")
                                else:
                                    logger.error(f"AI分析失败: {msg}")
                                    continue

                            if summary and len(summary) > 0:
                                summaries.append({
                                    "playletId": playlet_id,
                                    "videoUrl": video_url,
                                    "materialId": material_id,
                                    "adTitle": ad_title,
                                    "summary": summary
                                })

                        except Exception as e:
                            logger.error(f"处理视频失败 {video_url}: {traceback.format_exc()}")
                            continue

                # 保存摘要并更新到Strapi
                if len(summaries) > 0:
                    progress(i / total * 0.9, desc=f"上传AI解析内容: {len(summaries)}")
                    with open(playlet_info_path.replace(".json", "_summary.json"), "w", encoding="utf-8") as f:
                        json.dump(summaries, f, ensure_ascii=False, indent=4)

                    data = {
                        "playlet_id": str(playlet_id),
                        "ai_desc": [s["summary"] for s in summaries]
                    }

                    episodes, msg = sync_processor.find_episodes(data, locale)
                    processor.append_to_output(msg)

                    if episodes and len(episodes["data"]) > 0:
                        document_id = episodes["data"][0]["documentId"]
                        episodes, msg = sync_processor.update_episodes(document_id, data, locale)
                        processor.append_to_output(msg)
                
                progress(i / total, desc=f"处理剧集完成: {playlet_id}")
                processor.append_to_output(f"Processing data for playletId {playlet_id} has finished.")
                processed += 1

            except Exception as e:
                processor.append_to_output(f"Error processing data for playletId {playlet_id}: {traceback.format_exc()}")

        progress(1.0, desc="AI解析完成")
        processor.append_to_output(f"Summary finished. Total: {total}, Processed: {processed}")
        logger.info(f"{environment}环境视频AI解析完成")
        return f"AI解析完成！总计: {total}, 成功处理: {processed}"

    except Exception as e:
        error_msg = f"AI解析失败: {str(e)}"
        logger.error(error_msg)
        return error_msg

def refresh_logs():
    """刷新日志显示"""
    return logger.get_recent_logs()

def preview_content(file_path):
    """预览文件内容"""
    if not file_path:
        return "请选择文件", gr.update(visible=False), gr.update(visible=False), gr.update(visible=False)
    
    file_extension = os.path.splitext(file_path)[1].lower()
    
    if file_extension in ['.txt', '.json', '.md']:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return content, gr.update(visible=True), gr.update(visible=False), gr.update(visible=False)
        except Exception as e:
            return f"读取文件错误: {e}", gr.update(visible=False), gr.update(visible=False), gr.update(visible=False)
    elif file_extension in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
        return "", gr.update(visible=False), gr.update(visible=True, value=file_path), gr.update(visible=False)
    elif file_extension in ['.mp4', '.avi', '.mov']:
        return "", gr.update(visible=False), gr.update(visible=False), gr.update(visible=True, value=file_path)
    else:
        return "不支持的文件类型", gr.update(visible=False), gr.update(visible=False), gr.update(visible=False)

def get_yesterday():
    """获取昨天的日期"""
    return (datetime.now() - timedelta(1)).strftime("%Y-%m-%d")

def main():
    """创建并启动Gradio界面"""

    with gr.Blocks(title=GRADIO_CONFIG["title"]) as demo:
        gr.Markdown(f"# {GRADIO_CONFIG['title']}")
        
        with gr.Row():
            with gr.Column(scale=1):
                day_input = gr.Textbox(
                    label="日期 (YYYY-MM-DD)", 
                    value=get_yesterday,
                    placeholder="YYYY-MM-DD"
                )
            with gr.Column(scale=3):
                intersection = gr.TextArea(
                    label="抓包信息", 
                    value=DEFAULT_HEADERS,
                    lines=5
                )
        
        with gr.Row():
            with gr.Column():
                gr.Markdown("### 数据爬取")
                crawl_hot_btn = gr.Button("爬取热力榜", variant="primary")
                crawl_page_btn = gr.Button("爬取详情页")
                
            with gr.Column():
                gr.Markdown("### 线上环境")
                sync_online_btn = gr.Button("同步数据")
                summary_online_btn = gr.Button("AI解析剧情")
            with gr.Column():
                gr.Markdown("### 测试环境")
                summary_test_btn = gr.Button("同步数据")
                sync_test_btn = gr.Button("AI解析剧情")
        
        with gr.Row():
            with gr.Column():
                gr.Markdown("### 文件浏览")
                file_browser = gr.FileExplorer(
                    label="文件浏览器", 
                    root_dir="./data", 
                    file_count="single"
                )
                preview_btn = gr.Button("预览文件")
                
            with gr.Column():
                gr.Markdown("### 文件预览")
                preview_text = gr.Textbox(label="文本预览", lines=20, visible=False)
                preview_image = gr.Image(label="图片预览", visible=False)
                preview_video = gr.Video(label="视频预览", visible=False)
        
        with gr.Row():
            with gr.Column():
                gr.Markdown("### 系统日志")
                refresh_log_btn = gr.Button("刷新日志", variant="secondary")
                log_output = gr.Textbox(
                    label="日志输出", 
                    lines=15, 
                    max_lines=30,
                    interactive=False
                )
        
        # 事件绑定
        crawl_hot_btn.click(
            crawl_hot,
            inputs=[day_input, intersection],
            outputs=[log_output]
        )

        crawl_page_btn.click(
            crawl_detail_pages,
            inputs=[day_input, intersection],
            outputs=[log_output]
        )

        # 线上环境按钮
        sync_online_btn.click(
            lambda day: sync_to_environment(day, "online"),
            inputs=[day_input],
            outputs=[log_output]
        )

        summary_online_btn.click(
            lambda day: summary_videos(day, "online"),
            inputs=[day_input],
            outputs=[log_output]
        )

        # 测试环境按钮 (注意：按钮名称和功能对应)
        summary_test_btn.click(
            lambda day: sync_to_environment(day, "test"),
            inputs=[day_input],
            outputs=[log_output]
        )

        sync_test_btn.click(
            lambda day: summary_videos(day, "test"),
            inputs=[day_input],
            outputs=[log_output]
        )
        
        refresh_log_btn.click(
            refresh_logs,
            outputs=[log_output]
        )
        
        preview_btn.click(
            preview_content,
            inputs=[file_browser],
            outputs=[preview_text, preview_text, preview_image, preview_video]
        )
        
        # 页面加载时显示最新日志
        demo.load(refresh_logs, outputs=[log_output])
    
    demo.launch(
        server_name=GRADIO_CONFIG["server_name"],
        server_port=GRADIO_CONFIG["server_port"]
    )

if __name__ == "__main__":
    main()
