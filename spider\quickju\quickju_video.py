import pathlib
import requests
import os
import asyncio
import aiohttp
import aiofiles
import tempfile
from urllib.parse import urlparse, urljoin
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup
import logging
import traceback 
import shutil

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(module)s - %(message)s')

logger = logging.getLogger(__name__)


class QuickVideo:
    """QuickJu视频下载器"""

    def __init__(self, timeout=30, max_retries=3):
        self.timeout = timeout
        self.max_retries = max_retries
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.browser = None
        self.context = None
        self.page = None

    async def get_video_page(self, url, episode=1):
        """
        使用requests和bs4解析页面，获取视频播放页面URL

        Args:
            url: QuickJu页面URL，如 https://www.quickju.com/duanju/wonengtingjianwenwuxinsheng.html

        Returns:
            str: 视频播放页面的URL
        """
        url = url.replace("duanju", "play").replace(".html", f"-1-{episode}.html")
        return url
    
    async def init_browser(self):
        """初始化浏览器实例"""
        if self.browser is None:
            self.p = await async_playwright().start()
            # 无头模式 headless=True 浏览器在后台启动，不打开可见窗口
            self.browser = await self.p.chromium.launch(headless=True)
            # self.browser = await self.p.chromium.launch(headless=False)

            self.context = await self.browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            )
            # 创建一个页面实例
            self.page = await self.context.new_page()
            logger.info("浏览器实例已初始化")

    async def close_browser(self):
        """关闭浏览器实例"""
        if self.browser:
            await self.browser.close()
            await self.p.stop()
            self.browser = None
            self.context = None
            logger.info("浏览器实例已关闭")

    async def get_video_src_from_play_page(self, video_page_url):
        """
        在视频播放页面获取iframe中video标签的src

        Args:
            video_page_url: 视频播放页面URL

        Returns:
            tuple: (video_src, blob_info)
        """
        try:
            # 确保浏览器已初始化
            await self.init_browser()
            
            # 使用已创建的页面实例，而不是创建新页面
            # 打开视频播放页面
            logger.info(f"正在打开视频播放页面: {video_page_url}")
            await self.page.goto(video_page_url, timeout=120_000, wait_until="domcontentloaded")
            await self.page.wait_for_timeout(1000)

            # 等待iframe加载
            logger.info("等待iframe加载...")
            await self.page.wait_for_selector('#playleft > iframe', timeout=2000)

            # 获取iframe
            iframe_element = await self.page.query_selector('#playleft > iframe')
            if not iframe_element:
                raise ValueError("未找到id='buffer'的iframe")

            # 切换到iframe内容
            iframe = await iframe_element.content_frame()
            if not iframe:
                raise ValueError("无法获取iframe内容")

            # 在iframe中查找video标签
            logger.info("在iframe中查找video标签...")
            await iframe.wait_for_selector('video', timeout=15000)
            video_element = await iframe.query_selector('video')
            if not video_element:
                raise ValueError("在iframe中未找到video标签")

            # 获取video的src属性
            video_src = await video_element.get_attribute('src')
            if not video_src:
                raise ValueError("video标签没有src属性")

            logger.info(f"获取到video src: {video_src}")
            return video_src

        except Exception as e:
            logger.error(f"从播放页面获取video src失败: {traceback.format_exc()}")
            raise

    async def download_m3u8_and_merge_to_ts(self, m3u8_url, output_path, temp_dir=None):
        """
        下载m3u8文件并合并成ts视频文件

        Args:
            m3u8_url: m3u8文件的URL
            output_path: 输出ts文件路径
            temp_dir: 临时目录，如果为None则自动创建

        Returns:
            str: 输出文件路径
        """
        if temp_dir is None:
            temp_dir = tempfile.mkdtemp()

        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 下载m3u8文件
            m3u8_content = await self._download_m3u8_file(m3u8_url)

            # 解析m3u8文件，获取视频片段列表
            m3u8_urls = self._parse_m3u8_content(m3u8_content, m3u8_url)

            segment_urls = []
            for url in m3u8_urls:
                if url.endswith('.m3u8'):
                    m3u8_content = await self._download_m3u8_file(url)
                    segment_urls.extend(self._parse_m3u8_content(m3u8_content, m3u8_url))
                else:
                    segment_urls.append(url)

            if not segment_urls:
                raise ValueError("m3u8文件中没有找到视频片段")

            logger.info(f"找到 {len(segment_urls)} 个视频片段")

            # 下载所有视频片段
            segment_files = await self._download_video_segments(segment_urls, temp_dir)

            # 合并视频片段为ts
            await self._merge_segments_to_ts(segment_files, output_path)

            logger.info(f"视频合并完成: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"下载和转换m3u8失败: {traceback.format_exc()}")
            raise
        finally:
            # 清理临时文件
            if temp_dir and os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                    logger.info(f"清理临时目录: {temp_dir}")
                except Exception as e:
                    logger.warning(f"清理临时目录失败: {traceback.format_exc()}")

    async def download_m3u8_and_convert_to_mp4(self, m3u8_url, output_path, temp_dir=None):
        """
        下载m3u8文件并合并成ts视频文件

        Args:
            m3u8_url: m3u8文件的URL
            output_path: 输出ts文件路径
            temp_dir: 临时目录，如果为None则自动创建

        Returns:
            str: 输出文件路径
        """
        if temp_dir is None:
            temp_dir = tempfile.mkdtemp()

        try:
            # 确保输出目录存在
            output_path = output_path.replace('.mp4', '.ts')
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 下载m3u8文件
            m3u8_content = await self._download_m3u8_file(m3u8_url)

            # 解析m3u8文件，获取视频片段列表
            m3u8_urls = self._parse_m3u8_content(m3u8_content, m3u8_url)

            segment_urls = []
            for url in m3u8_urls:
                if url.endswith('.m3u8'):
                    m3u8_content = await self._download_m3u8_file(url)
                    segment_urls.extend(self._parse_m3u8_content(m3u8_content, m3u8_url))
                else:
                    segment_urls.append(url)

            if not segment_urls:
                raise ValueError("m3u8文件中没有找到视频片段")

            logger.info(f"找到 {len(segment_urls)} 个视频片段")

            # 下载所有视频片段
            segment_files = await self._download_video_segments(segment_urls, temp_dir)

            # 合并视频片段为ts
            await self._merge_segments_to_ts(segment_files, output_path)

            await self._convert_ts_to_mp4(output_path, output_path.replace('.ts', '.mp4'))
            os.remove(output_path)

            logger.info(f"视频合并完成: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"下载和转换m3u8失败: {traceback.format_exc()}")
            raise
        finally:
            # 清理临时文件
            if temp_dir and os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                    logger.info(f"清理临时目录: {temp_dir}")
                except Exception as e:
                    logger.warning(f"清理临时目录失败: {traceback.format_exc()}")

    # async def download_m3u8_and_convert_to_mp4(self, m3u8_url, output_path, temp_dir=None):
    #     """
    #     下载m3u8文件，合并为ts视频文件，然后转换为mp4格式

    #     Args:
    #         m3u8_url: m3u8文件的URL
    #         output_path: 输出mp4文件路径
    #         temp_dir: 临时目录，如果为None则自动创建

    #     Returns:
    #         str: 输出文件路径
    #     """
    #     if temp_dir is None:
    #         temp_dir = tempfile.mkdtemp()

    #     # 确保输出目录存在
    #     os.makedirs(os.path.dirname(output_path), exist_ok=True)

    #     # 生成临时ts文件路径
    #     # ts_output_path = os.path.join(temp_dir, "temp_video.ts")
    #     ts_output_path = pathlib.Path(temp_dir) / "temp_video.ts"

    #     try:
    #         # 先合并为ts文件
    #         ts_path = await self.download_m3u8_and_merge_to_ts(m3u8_url, output_path, temp_dir)

    #         # 将ts文件转换为mp4
    #         await self._convert_ts_to_mp4(ts_path, output_path)

    #         logger.info(f"视频转换完成: {output_path}")
    #         return output_path

    #     except Exception as e:
    #         logger.error(f"下载和转换m3u8失败: {traceback.format_exc()}")
    #         raise
    #     finally:
    #         # 清理临时文件
    #         if temp_dir and os.path.exists(temp_dir):
    #             try:
    #                 shutil.rmtree(temp_dir)
    #                 logger.info(f"清理临时目录: {temp_dir}")
    #             except Exception as e:
    #                 logger.warning(f"清理临时目录失败: {traceback.format_exc()}")

    async def _download_m3u8_file(self, m3u8_url):
        """下载m3u8文件内容"""
        async with aiohttp.ClientSession() as session:
            async with session.get(m3u8_url) as response:
                response.raise_for_status()
                content = await response.text()
                logger.info(f"下载m3u8文件成功，大小: {len(content)} 字符")
                return content

    def _parse_m3u8_content(self, m3u8_content, base_url):
        """
        解析m3u8文件内容，提取视频片段URL列表

        Args:
            m3u8_content: m3u8文件内容
            base_url: m3u8文件的基础URL，用于构建相对路径

        Returns:
            list: 视频片段URL列表
        """
        segment_urls = []
        base_url_parsed = urlparse(base_url)
        base_path = f"{base_url_parsed.scheme}://{base_url_parsed.netloc}"

        for line in m3u8_content.split('\n'):
            line = line.strip()

            # 跳过注释行和空行
            if not line or line.startswith('#'):
                continue

            # 构建完整的URL
            if line.startswith('http'):
                segment_url = line
            else:
                segment_url = f"{base_path}{line}"

            segment_urls.append(segment_url)

        return segment_urls

    async def _download_video_segments(self, segment_urls, temp_dir):
        """
        异步下载所有视频片段

        Args:
            segment_urls: 视频片段URL列表
            temp_dir: 临时目录

        Returns:
            list: 下载的片段文件路径列表
        """
        segment_files = []

        async with aiohttp.ClientSession() as session:
            tasks = []

            for i, url in enumerate(segment_urls):
                segment_file = os.path.join(temp_dir, f"segment_{i:04d}.ts")
                task = self._download_single_segment(session, url, segment_file)
                tasks.append((task, segment_file))

            # 并发下载所有片段
            for task, segment_file in tasks:
                try:
                    await task
                    segment_files.append(segment_file)
                    logger.info(f"下载片段成功: {segment_file}")
                except Exception as e:
                    logger.error(f"下载片段失败 {segment_file}: {traceback.format_exc()}")
                    # 继续下载其他片段
        logger.info(f"成功下载 {len(segment_files)} 个视频片段")
        return segment_files

    async def _download_single_segment(self, session, url, filepath):
        """下载单个视频片段"""
        for attempt in range(self.max_retries):
            try:
                async with session.get(url, timeout=self.timeout) as response:
                    response.raise_for_status()

                    async with aiofiles.open(filepath, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            await f.write(chunk)

                    return

            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"下载片段重试 {attempt + 1}/{self.max_retries}: {url}")
                    await asyncio.sleep(1)
                else:
                    raise e

    async def _merge_segments_to_ts(self, segment_files, output_path):
        """
        直接连接视频片段为ts文件（不使用ffmpeg）

        Args:
            segment_files: 视频片段文件路径列表
            output_path: 输出ts文件路径
        """
        if not segment_files:
            raise ValueError("没有视频片段可以合并")

        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # 按顺序连接所有片段
        async with aiofiles.open(output_path, 'wb') as outfile:
            for segment_file in segment_files:
                try:
                    async with aiofiles.open(segment_file, 'rb') as infile:
                        # 读取片段文件并写入输出文件
                        while True:
                            chunk = await infile.read(8192)  # 8KB chunks
                            if not chunk:
                                break
                            await outfile.write(chunk)
                    logger.info(f"已连接片段: {segment_file}")
                except Exception as e:
                    logger.error(f"连接片段失败 {segment_file}: {traceback.format_exc()}")
                    raise

        logger.info(f"视频片段连接成功: {output_path}")

    async def _convert_ts_to_mp4(self, ts_path, mp4_path):
        """
        使用ffmpeg将ts文件转换为mp4文件

        Args:
            ts_path: 输入ts文件路径
            mp4_path: 输出mp4文件路径
        """
        # 确保输出目录存在
        os.makedirs(os.path.dirname(mp4_path), exist_ok=True)

        # 构建ffmpeg命令
        cmd = [
            'ffmpeg',
            '-i', ts_path,  # 输入ts文件
            '-c', 'copy',   # 直接复制流，不重新编码
            '-y',  # 覆盖输出文件
            mp4_path  # 输出mp4文件
        ]

        # 执行ffmpeg命令
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        _, stderr = await process.communicate()

        if process.returncode != 0:
            error_msg = stderr.decode('utf-8') if stderr else "未知错误"
            raise RuntimeError(f"ffmpeg转换失败: {error_msg}")

        logger.info(f"ts转mp4成功: {mp4_path}")

    async def process_video(self, quickju_url, output_path, episode=1):
        """
        完整的视频处理流程

        Args:
            quickju_url: QuickJu页面URL
            output_path: 输出文件路径（.ts 或 .mp4）

        Returns:
            str: 输出文件路径
        """
        try:
            logger.info(f"开始处理视频: {quickju_url}")
            base_path = os.path.dirname(output_path)
            os.makedirs(base_path, exist_ok=True)
            temp_path = base_path + "/tmp"
            os.makedirs(temp_path, exist_ok=True)

            # 步骤1: 使用requests和bs4解析页面获取视频播放页面URL
            video_page_url = await self.get_video_page(quickju_url, episode)

            # 步骤2: 在视频播放页面获取video src
            video_src = await self.get_video_src_from_play_page(video_page_url)

            # 步骤3: 检查是否是m3u8文件
            if video_src.endswith('.m3u8'):
                logger.info(f"检测到m3u8格式，开始下载和合并, {video_src}")
                # 根据输出路径扩展名决定处理方式
                if output_path.endswith('.mp4'):
                    logger.info("输出格式为mp4，将合并ts片段后转换为mp4")
                    # 步骤4: 下载m3u8并转换为mp4
                    result_path = await self.download_m3u8_and_convert_to_mp4(video_src, output_path, temp_path)
                else:
                    logger.info("输出格式为ts，将合并ts片段")
                    # 步骤4: 下载m3u8并合并为ts
                    result_path = await self.download_m3u8_and_merge_to_ts(video_src, output_path, temp_path)
                return result_path
            else:
                logger.info(f"未检测到m3u8格式，尝试直接下载, {video_src}")
                # 如果不是m3u8，尝试直接下载
                return await self._download_direct_video(video_src, output_path)

        except Exception as e:
            logger.error(f"处理视频失败: {traceback.format_exc()}")
            raise

    async def _download_direct_video(self, video_url, output_path):
        """
        直接下载视频文件

        Args:
            video_url: 视频URL
            output_path: 输出文件路径

        Returns:
            str: 输出文件路径
        """
        try:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            async with aiohttp.ClientSession() as session:
                async with session.get(video_url) as response:
                    response.raise_for_status()

                    async with aiofiles.open(output_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            await f.write(chunk)

            logger.info(f"直接下载视频完成: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"直接下载视频失败: {traceback.format_exc()}")
            raise


# 使用示例
async def main():
    """使用示例"""
    downloader = QuickVideo()

    # 初始化浏览器
    await downloader.init_browser()

    try:
        # 示例URL
        url = "https://www.quickju.com/duanju/wonengtingjianwenwuxinsheng.html"
        # 后缀名为 .ts或者.mp4, 后缀名是什么就保存为相应的格式存储。
        output_path = "./data/quickju/video.ts"

        result_path = await downloader.process_video(url, output_path)
        print(f"视频下载完成: {result_path}")
    except Exception as e:
        print(f"下载失败: {traceback.format_exc()}")
    finally:
        # 关闭浏览器
        await downloader.close_browser()

if __name__ == "__main__":
    asyncio.run(main())

