import logging
import os
from datetime import datetime
from config import LOG_FILE, LOG_MAX_LINES

from logging.handlers import RotatingFileHandler # 导入 RotatingFileHandler

# 定义日志文件路径和最大行数 (或这里是最大文件大小和备份文件数)
# 注意：LOG_FILE 和 LOG_MAX_LINES 应该是定义在你的程序顶部的全局常量
# 这里为了示例，我将它们定义在类外面。
LOG_DIR = 'logs'
LOG_FILE_NAME = 'app.log'
LOG_FILE = os.path.join(LOG_DIR, LOG_FILE_NAME)
LOG_MAX_BYTES = 10 * 1024 * 1024  # 10 MB
LOG_BACKUP_COUNT = 5             # 保留 5 个备份文件

class AppLogger:
    def __init__(self):
        # 确保日志目录存在
        os.makedirs(os.path.dirname(LOG_FILE), exist_ok=True)
        
        # 获取根 logger
        # 注意：这里我们配置的是根 logger，而不是 self.logger = logging.getLogger(__name__)
        # 如果你希望 AppLogger 实例控制整个应用的日志，直接配置根 logger 更合适。
        # 如果你想为每个模块单独配置 logger，那么 getLogger(__name__) 是正确的，
        # 但你可能需要更细致地管理 handler 的添加，避免重复添加。
        # 对于一个全局的 AppLogger 实例，配置根 logger 通常更简单。
        self.logger = logging.getLogger() # 获取根 logger
        self.logger.setLevel(logging.INFO) # 设置根 logger 的级别

        # 清除可能已存在的 handlers，防止重复添加
        if not self.logger.handlers:
            # 配置 RotatingFileHandler
            file_handler = RotatingFileHandler(
                LOG_FILE,
                maxBytes=LOG_MAX_BYTES,  # 文件达到 10MB 时滚动
                backupCount=LOG_BACKUP_COUNT, # 保留 5 个旧日志文件
                encoding='utf-8'
            )
            file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(module)s - %(message)s'))
            self.logger.addHandler(file_handler)
            
            # 配置 StreamHandler (控制台输出)
            stream_handler = logging.StreamHandler()
            stream_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(module)s - %(message)s'))
            self.logger.addHandler(stream_handler)

    def info(self, message):
        """记录信息日志"""
        self.logger.info(message)
    
    def error(self, message):
        """记录错误日志"""
        self.logger.error(message)
    
    def warning(self, message):
        """记录警告日志"""
        self.logger.warning(message)
    
    def get_recent_logs(self, lines=LOG_MAX_LINES):
        """获取最近的日志，模仿tail命令的行为"""
        try:
            if not os.path.exists(LOG_FILE):
                return "日志文件不存在"
            
            # 使用缓冲区大小，通常足够大以包含所需行数
            buffer_size = 8192
            with open(LOG_FILE, 'rb') as f:
                # 移动到文件末尾
                f.seek(0, os.SEEK_END)
                file_size = f.tell()
                
                # 初始化变量
                lines_found = []
                blocks = []
                block_count = 1
                
                # 从文件末尾开始读取块，直到找到足够的行或读取完整个文件
                while len(lines_found) < lines and file_size > 0:
                    # 计算读取位置
                    if file_size > buffer_size * block_count:
                        f.seek(file_size - buffer_size * block_count)
                        block_data = f.read(buffer_size)
                    else:
                        f.seek(0)
                        block_data = f.read(file_size)
                    
                    # 解码并分割行
                    try:
                        block_lines = block_data.decode('utf-8').splitlines(True)
                    except UnicodeDecodeError:
                        # 如果解码失败，尝试忽略错误
                        block_lines = block_data.decode('utf-8', errors='ignore').splitlines(True)
                    
                    # 将当前块的行添加到结果中
                    lines_found = block_lines + lines_found
                    
                    # 更新读取的块数和文件大小
                    block_count += 1
                    if file_size > buffer_size * block_count:
                        file_size = file_size - buffer_size * (block_count - 1)
                    else:
                        break
                
                # 返回最后的指定行数
                if len(lines_found) > lines:
                    lines_found = lines_found[-lines:]
                
                return ''.join(lines_found)
        except Exception as e:
            return f"读取日志失败: {str(e)}"

# 全局日志实例
logger = AppLogger()
