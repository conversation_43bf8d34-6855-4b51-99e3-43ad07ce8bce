import json
import os
import re
import requests
from bs4 import BeautifulSoup
from datetime import datetime
from tqdm import tqdm
import asyncio
from quickju.quickju_video import QuickVideo
from logger import logger

# Define User-Agent globally so it can be used by all functions

def load_or_create_np_mapping():
    """加载或创建name-playletId映射文件"""
    np_file = "data/name_playlet_mapping.np"
    
    # 如果文件存在，加载它
    if os.path.exists(np_file):
        try:
            with open(np_file, "r", encoding="utf-8") as f:
                np_mapping = json.load(f)
            print(f"已加载 {len(np_mapping)} 个已存在的映射")
            return np_mapping
        except Exception as e:
            print(f"加载映射文件失败: {e}")
            return {}
    else:
        # 如果文件不存在，返回空字典
        return {}

def save_np_mapping(np_mapping):
    """保存name-playletId映射到文件"""
    try:
        os.makedirs("data", exist_ok=True)
        np_file = "data/name_playlet_mapping.np"
        with open(np_file, "w", encoding="utf-8") as f:
            json.dump(np_mapping, f, ensure_ascii=False, indent=2)
        print(f"已保存 {len(np_mapping)} 个映射到 {np_file}")
    except Exception as e:
        print(f"保存映射文件失败: {e}")

def crawl_dramas(page_num = 1, num_episodes=None):
    """爬取网站剧集信息"""
    UA = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
    HOST = "https://www.quickju.com"
    
    # 加载或创建name-playletId映射
    np_mapping = load_or_create_np_mapping()
    
    # 获取当前最大的playletId，用于新剧集的ID分配
    max_playlet_id = 10000
    if np_mapping:
        max_playlet_id = max([abs(int(pid)) for pid in np_mapping.values()])
    
    # ---------- 1. 首页 40 条基本卡片 ----------
    # https://www.quickju.com/vodshow/1--------3---.html
    try:

        idx_html = requests.get(f"{HOST}/vodshow/1--------{page_num}---.html", headers=UA, timeout=60).text
        soup = BeautifulSoup(idx_html, "lxml")
    except Exception as e:
        print(f"爬取首页失败: {e}")
        return None
    
    today = datetime.today().strftime("%Y-%m-%d")
    items = []
    
    for idx, card in enumerate(soup.select(".module-item"), start=10000):
        title = card.select_one(".video-name a")["title"].strip()
        detail_url = HOST + card.select_one(".video-name a")["href"]
        cover = card.select_one(".module-item-pic img").get("data-src") or \
                card.select_one(".module-item-pic img").get("src")
        
        # 检查是否已存在该名称的映射
        if title in np_mapping:
            playlet_id = abs(int(np_mapping[title]))  # 使用已存在的playletId
        else:
            # 为新剧集分配新的playletId
            max_playlet_id += 1
            playlet_id = max_playlet_id
            np_mapping[title] = str(-playlet_id)  # 添加到映射中
        
        items.append({
            "playletId": -playlet_id,  # 保持负数格式以兼容现有代码
            "materialId": f"mat_{playlet_id:03d}",
            "name": title,
            "detail_url": detail_url,
            "coverUrl": cover
        })
    
    # 保存更新后的映射
    save_np_mapping(np_mapping)
    
    # ---------- 2. 详情页精准解析 ----------
    def parse_detail(item):
        try:
            html = requests.get(item["detail_url"], headers=UA, timeout=10).text
            s = BeautifulSoup(html, "lxml")
    
            # 1. 完整剧情（去掉 “展开 / 收起”）
            plot = s.select_one(".vod_content span")
            if plot:
                for a in plot.select("a"):
                    a.decompose()                       # 去掉 <a>展开/收起</a>
                description = re.sub(r"\s+", " ", plot.get_text(" ", strip=True))
            else:
                description = ""
    
            # 2. 更新时间
            update_text = s.find("span", string=re.compile("更新："))
            publish_time = ""
            if update_text:
                publish_time = update_text.find_next_sibling("div").get_text(strip=True)
                publish_time = publish_time.split("，")[0].strip()          # 去掉“最后更新于”
    
            # 3. 集数
            remark_text = s.find("span", string=re.compile("备注："))
            episodes = ""
            if remark_text:
                episodes = remark_text.find_next_sibling("div").get_text(strip=True)
    
            # 2. TAG 标签
            tag_span = s.find("span", string=re.compile("TAG："))
            keywords = []
            if tag_span:
                tag_div = tag_span.find_next_sibling("div", class_="video-info-item")
                keywords = [a.get_text(strip=True) for a in tag_div.select("a")]

            # 3. TAG 分类标签
            category = [a.get_text(strip=True) for a in s.select(".tag-link a")]

            # 5. 导演 / 主演（按站内字段名）
            director = ""
            actors   = ""
            for li in s.select(".video-info-items"):
                txt = li.get_text(" ", strip=True)
                if "导演：" in txt:
                    director = txt.split("：", 1)[-1].strip()
                if "主演：" in txt:
                    actors   = txt.split("：", 1)[-1].strip()
            
            # 将actors字符串转换为数组
            actor_list = [actor.strip() for actor in actors.split("/") if actor.strip()] if actors else []
    
            item.update({
                "description": description,
                "keywords": keywords,  # 保持为列表格式
                "category": category,  # 保持为列表格式
                "director": director,
                "actor": actor_list,  # 转换为列表格式
                "publishTime": publish_time,
                "episodes": episodes
            })
        except Exception as e:
            item["error"] = str(e)
    
    # 如果指定了num_episodes，则只保留前num_episodes个剧集
    if num_episodes is not None and num_episodes > 0:
        items = items[:num_episodes]
    
    # 爬取详情页信息
    print("开始爬取详情页信息...")
    for it in tqdm(items, desc="爬取详情"):
        parse_detail(it)
        import time; time.sleep(0.3)  # 添加延迟避免请求过快
    
    # 清理数据，移除不需要的字段
    for item in items:
        # 保留 detail_url 字段
        # if "detail_url" in item:
        #     del item["detail_url"]
        # 如果有错误信息，也可以移除
        if "error" in item:
            del item["error"]
    
    result = {"date": today, "content": items}
    return result

def save_dramas_to_file(dramas_data, data_dir, num_episodes=None):
    """
    保存剧集数据到文件
    """
    try:
        # 使用当天日期作为文件名
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
        filename = f"{data_dir}/data.json"
        
        # 如果指定了num_episodes，则只保存前num_episodes个剧集
        if num_episodes is not None and num_episodes > 0:
            # 创建一个新的数据副本，只包含指定数量的剧集
            filtered_data = dramas_data.copy()
            filtered_data["content"] = dramas_data["content"][:num_episodes]
        else:
            # 如果没有指定或指定为无效值，则保存所有剧集
            filtered_data = dramas_data
            
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(filtered_data, f, ensure_ascii=False, indent=2)
        print(f"已生成 {filename}")
        return True
    except Exception as e:
        print(f"保存文件失败: {e}")
        return False

def download_media_files(dramas_data, covers_dir):
    """下载媒体文件（封面图片）"""
    
    # 确保目录存在
    os.makedirs(covers_dir, exist_ok=True)
    
    max_attempts = 5  # 最大尝试次数
    attempt = 0
    total_downloaded = 0
    
    while attempt < max_attempts:
        attempt += 1
        logger.info(f"开始第 {attempt} 次封面下载循环")
        
        # 准备下载任务
        download_tasks = []
        
        for item in dramas_data.get("content", []):
            if isinstance(item, dict):
                playlet_id = item.get("playletId", "")
                cover_url = item.get("coverUrl", "")
                if cover_url and playlet_id:
                    # 生成文件名
                    filename = f"{playlet_id}_cover.jpg"
                    filepath = os.path.join(covers_dir, filename)
                    
                    # 检查文件是否已存在，如果存在则跳过下载
                    if os.path.exists(filepath):
                        logger.info(f"封面文件已存在，跳过下载: {filepath}")
                        total_downloaded += 1
                        continue
                        
                    download_tasks.append((cover_url, filepath))
        
        # 如果没有需要下载的任务，直接退出
        if not download_tasks:
            logger.info("没有需要下载的封面，退出循环")
            break
            
        # 统计本次循环下载成功的数量
        downloaded_in_this_loop = 0
        segments_found_in_this_loop = False  # 标记本次循环是否成功下载了封面
        
        # 下载图片
        for cover_url, filepath in download_tasks:
            try:
                logger.info(f"正在下载封面: {cover_url}")
                response = requests.get(cover_url, verify=False, timeout=50)
                response.raise_for_status()
                
                with open(filepath, "wb") as f:
                    f.write(response.content)
                
                downloaded_in_this_loop += 1
                total_downloaded += 1
                segments_found_in_this_loop = True  # 成功下载了封面
                logger.info(f"封面下载成功: {filepath}")
            except Exception as e:
                logger.error(f"下载封面失败 {cover_url}: {e}")
        
        # 如果本次循环没有成功下载任何封面，则退出循环
        if not segments_found_in_this_loop:
            logger.info(f"第 {attempt} 次循环没有成功下载新的封面，退出循环")
            break
        else:
            logger.info(f"第 {attempt} 次循环完成，本次下载了 {downloaded_in_this_loop} 个封面")
    
    logger.info(f"媒体文件下载完成，总共成功: {total_downloaded}/{len(dramas_data.get('content', []))}")
    return total_downloaded

async def download_drama_videos(dramas_data, videos_dir, num_episodes=3, video_format="mp4"):
    """下载所有剧集的视频"""

    # 确保目录存在
    os.makedirs(videos_dir, exist_ok=True)
    
    max_attempts = 5  # 最大尝试次数
    attempt = 0
    total_downloaded = 0
    
    while attempt < max_attempts:
        attempt += 1
        logger.info(f"开始第 {attempt} 次视频下载循环")
        
        # 初始化视频下载器
        downloader = QuickVideo()
        
        # 初始化浏览器
        await downloader.init_browser()
        
        try:
            # 统计本次循环下载成功的视频数量
            downloaded_in_this_loop = 0
            segments_found_in_this_loop = False  # 标记本次循环是否找到视频片段
            
            # 遍历所有剧集
            for item in tqdm(dramas_data.get("content", []), desc=f"下载视频 (第 {attempt} 次循环)"):
                if isinstance(item, dict):
                    playlet_id = item.get("playletId", "")
                    detail_url = item.get("detail_url", "")
                    # 将playlet_id的负数变为正数
                    playlet_id = abs(int(playlet_id))

                    if detail_url and playlet_id:
                        # 为每一集下载视频（根据指定的集数）
                        for episode in range(1, num_episodes + 1):
                            try:
                                # 生成视频文件路径
                                video_filename = f"{playlet_id}_episode_{episode:03d}.{video_format}"
                                video_filepath = os.path.join(videos_dir, video_filename)
                                
                                # 检查文件是否已存在
                                if os.path.exists(video_filepath):
                                    logger.info(f"视频已存在，跳过: {video_filepath}")
                                    downloaded_in_this_loop += 1
                                    total_downloaded += 1
                                    continue
                                
                                # 下载视频
                                logger.info(f"正在下载第 {episode} 集视频: {detail_url}")
                                
                                # 使用异步方式下载视频
                                await downloader.process_video(detail_url, video_filepath, episode)
                                
                                downloaded_in_this_loop += 1
                                total_downloaded += 1
                                segments_found_in_this_loop = True  # 找到并成功下载了视频片段
                                logger.info(f"第 {episode} 集视频下载成功: {video_filepath}")
                            except Exception as e:
                                logger.error(f"下载第 {episode} 集视频失败 {detail_url}: {e}")
                                # 如果第一集就失败了，可能这个剧集没有分集，尝试下载默认集
                                if episode == 1:
                                    try:
                                        # 生成视频文件路径
                                        video_filename = f"{playlet_id}_video.{video_format}"
                                        video_filepath = os.path.join(videos_dir, video_filename)
                                        
                                        # 检查文件是否已存在
                                        if os.path.exists(video_filepath):
                                            logger.info(f"视频已存在，跳过: {video_filepath}")
                                            downloaded_in_this_loop += 1
                                            total_downloaded += 1
                                            continue
                                        
                                        # 下载视频
                                        logger.info(f"正在下载视频: {detail_url}")
                                        
                                        # 使用异步方式下载视频
                                        await downloader.process_video(detail_url, video_filepath)
                                        
                                        downloaded_in_this_loop += 1
                                        total_downloaded += 1
                                        segments_found_in_this_loop = True  # 找到并成功下载了视频片段
                                        logger.info(f"视频下载成功: {video_filepath}")
                                    except Exception as e2:
                                        logger.error(f"下载默认集视频失败 {detail_url}: {e2}")
                                # 不再break，继续尝试下一集
                        # 如果第一集下载失败，仍然继续处理其他集数
                    else:
                        logger.warning(f"缺少 detail_url 或 playletId: {item}")
            
            # 如果本次循环没有找到任何新的视频片段，则退出循环
            if not segments_found_in_this_loop:
                logger.info(f"第 {attempt} 次循环没有找到新的视频片段，退出循环")
                break
            else:
                logger.info(f"第 {attempt} 次循环完成，本次下载了 {downloaded_in_this_loop} 个视频")
                
        finally:
            # 关闭浏览器
            await downloader.close_browser()
    
    logger.info(f"视频下载完成，总共成功下载: {total_downloaded}/{len(dramas_data.get('content', []))}")
    return total_downloaded

def main():
    # 获取当前日期
    today = datetime.today().strftime("%Y-%m-%d")

    data_dir = f"data/{today.replace('-', '/')}"
    data_file = f"{data_dir}/data.json"

    if not os.path.exists(data_file):
        print("开始爬取剧集信息...")

        # 爬取剧集信息
        dramas_data = crawl_dramas()
        if not dramas_data:
            print("爬取剧集信息失败")
            return
        
        print(f"成功爬取 {len(dramas_data['content'])} 个剧集")
        save_dramas_to_file(dramas_data)

    else:
        with open(data_file, "r", encoding="utf-8") as f:
            dramas_data = json.load(f)

    # # 下载媒体文件并保存数据到新路径
    # downloaded = download_media_files(today, dramas_data)
    
    # # 下载所有剧集的视频（默认下载前3集，保存为mp4格式）  后缀名为 .ts或者.mp4, 后缀名是什么就保存为相应的格式存储。
    # videos_downloaded = asyncio.run(download_drama_videos(today, dramas_data, num_episodes=3, video_format="ts"))
    
    # logger.info(f"爬取完成，总计: {len(dramas_data['content'])}, 下载媒体文件: {downloaded}, 下载视频: {videos_downloaded}")

if __name__ == "__main__":
    main()
