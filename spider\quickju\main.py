import asyncio
import json
import os
import subprocess
import pathlib
import sys
from datetime import datetime
from quickju.crawl import crawl_dramas, download_media_files, save_dramas_to_file, download_drama_videos
from processor import DataSyncProcessor
from logger import logger
from processor import VideoSummaryProcessor, DatabaseManager
from gradio_app import sync_to_environment_quickju
from typing import Union, Dict, Any

def load_dramas_from_file(filename):
    """从文件中加载剧集数据"""
    try:
        with open(filename, "r", encoding="utf-8") as f:
            dramas_data = json.load(f)
        logger.info(f"已从 {filename} 加载剧集数据")
        return dramas_data
    except Exception as e:
        logger.info(f"从文件加载数据失败: {e}")
        return None
    
def progress_callback(current, total, desc=""):
    """进度回调函数"""
    print(f"[{current}/{total}] {desc}")

def simple_progress(current, desc=""):
    """简单的进度回调函数"""
    print(f"[{current}] {desc}")


def extract_frames(
    dramas_data: Dict[str, Any],
    n: int,
    out_dir: Union[str, pathlib.Path],
    video_dir: Union[str, pathlib.Path] = "data/videos"
) -> None:
    """
    根据 dramas_data["content"] 中的 playletId，
    只处理 video_dir 下匹配前缀的 *.mp4，每部抽 n 帧写入 out_dir/playletId/。
    """
    try:
        video_dir = pathlib.Path(video_dir).expanduser().resolve()
        out_dir   = pathlib.Path(out_dir).expanduser().resolve()

        if not video_dir.is_dir():
            raise NotADirectoryError(video_dir)

        for item in dramas_data.get("content", []):
            if not isinstance(item, dict):
                continue

            playlet_id = str(abs(item.get("playletId", 0)))  # 负数变正数
            if not playlet_id:
                continue

            # 只找符合 playlet_id_*.mp4 的文件
            for video in sorted(video_dir.glob(f"{playlet_id}_*.mp4")):
                try:
                    series_id = playlet_id
                    out_path  = out_dir / series_id
                    out_path.mkdir(parents=True, exist_ok=True)

                    # 计算该剧集下已有多少张 jpg，决定下一张编号
                    existing   = sorted(out_path.glob("*.jpg"))
                    next_num   = 1
                    if existing:
                        last_num = int(existing[-1].stem.split('_')[1])
                        next_num = last_num + 1

                    episode_id = video.stem.split('_')[-1]
                    if episode_id == "episode" or episode_id == "video":
                        next_num = 1
                    else:
                        next_num = (int(episode_id) - 1) * n + 1

                    # 计算总帧数
                    probe_cmd = [
                        'ffprobe', '-v', 'error',
                        '-select_streams', 'v:0',
                        '-count_frames', '-show_entries',
                        'stream=nb_read_frames', '-of', 'csv=p=0',
                        str(video)
                    ]
                    total_frames = int(subprocess.check_output(probe_cmd, text=True).strip())
                    step = max(total_frames // n, 1)

                    # 抽帧
                    cmd = [
                        'ffmpeg', '-i', str(video),
                        '-vf', f'select=not(mod(n\,{step}))', '-vsync', 'vfr',
                        '-start_number', str(next_num),
                        str(out_path / f'{series_id}_%04d.jpg')
                    ]
                    subprocess.run(cmd, check=True)
                    logger.info(f'{video.name} → 已追加 {n} 帧到 {out_path}')
                except subprocess.CalledProcessError as e:
                    logger.error(f"处理视频 {video.name} 时发生错误: {e}")
                    continue
                except ValueError as e:
                    logger.error(f"处理视频 {video.name} 时数值转换错误: {e}")
                    continue
                except Exception as e:
                    logger.error(f"处理视频 {video.name} 时发生未知错误: {e}")
                    continue
    except Exception as e:
        logger.error(f"extract_frames 函数执行时发生错误: {e}")
        raise

def main():
    # 获取今天的日期
    # today = datetime(2025, 8, 14).strftime("%Y-%m-%d")   # 2025-08-14
     
    # python -m quickju.main 2025-08-26 1
    if len(sys.argv) > 1:
        today = sys.argv[1]
    else:
        today = datetime.today().strftime("%Y-%m-%d")
    if len(sys.argv) > 2:
        page_num = sys.argv[2]
        logger.info(f"爬取第 {page_num} 页的数据")
    else:
        page_num = 0
    
    if page_num == 0:
        data_dir = f"data/quickju/{today.replace('-', '/')}"
        num_episodes = 40
    else:
        data_dir = f"data/quickju/{today.replace('-', '/')}/{page_num}"
        num_episodes = None

        
    os.makedirs(data_dir, exist_ok=True)

    filename = f"{data_dir}/data.json"
    # 检查今天的数据文件是否存在
    if os.path.exists(filename):
        logger.info(f"找到今天的剧集数据文件 {filename}，直接加载...")
        dramas_data = load_dramas_from_file(filename)
        if not dramas_data:
            logger.info("加载剧集数据失败")
            return
    else:
        logger.info(f"未找到今天的剧集数据文件 {filename}，开始爬取...")
        # 爬取剧集信息
        dramas_data = crawl_dramas(page_num, num_episodes)
        if not dramas_data:
            logger.info("爬取剧集信息失败")
            return
        
        logger.info(f"成功爬取 {len(dramas_data['content'])} 个剧集")
        # 保存数据到新路径
        save_dramas_to_file(dramas_data, data_dir)

    # 下载媒体文件
    # 下载封面也会因为网络问题没下载好，也可以按照download_drama_videos的循环来搞一下
    covers_dir = f"data/quickju/covers"
    downloaded = download_media_files(dramas_data, covers_dir)
    # 下载所有剧集的视频（默认下载前3集，保存为mp4格式）  后缀名为 .ts或者.mp4, 后缀名是什么就保存为相应的格式存储。
    video_directory = f"data/quickju/videos"
    videos_downloaded = asyncio.run(download_drama_videos(dramas_data, video_directory, num_episodes=3, video_format="mp4"))
    logger.info(f"爬取完成，总计: {len(dramas_data['content'])}, 下载媒体文件: {downloaded}, 下载视频: {videos_downloaded}")

    # 创建coverlist图
    dir_coverlists = f"data/quickju/coverlists"
    extract_frames(dramas_data=dramas_data, n=3, out_dir=dir_coverlists, video_dir=video_directory)

    # 初始化数据库管理器
    db_manager = DatabaseManager()
    # 初始化视频摘要处理器
    processor = VideoSummaryProcessor("test", db_manager)
    # 处理目录中的所有视频文件
    logger.info(f"开始处理目录 '{video_directory}' 中的视频文件...")
    results = processor.process_video_directory(video_directory, dramas_data, progress_callback)
    logger.info(f"AI视频处理完成")

    # 同步数据到测试环境
    logger.info("开始同步数据到线上环境...")
    sync_result = sync_to_environment_quickju(data_dir, "online", simple_progress)
    logger.info(f"数据同步完成: {sync_result}")

if __name__ == "__main__":
    main()
